import type { VariantProps } from "class-variance-authority";
import { cva } from "class-variance-authority";
import type { ButtonProps as FlowbiteButtonProps } from "flowbite-react";
import { Button as FlowbiteButton, Spinner } from "flowbite-react";
import React, { type ForwardedRef, type PropsWithChildren } from "react";
import { forwardRef } from "react";
import type { Merge } from "type-fest";

import { cn } from "@/lib/utils";

const buttonVariants = cva(
  "px-3 py-2 rounded-lg flex items-center h-10 justify-center gap-2 [&>span]:p-0 [&>span]:flex [&>span]:items-center [&>span]:gap-2 !ring-0 duration-[20ms]",
  {
    variants: {
      variant: {
        default:
          "bg-primary-700 text-white hover:!bg-primary-600 dark:bg-primary-700 dark:hover:!bg-primary-500",
        green:
          "bg-green-500 text-white hover:!bg-green-600 dark:bg-green-600 dark:hover:!bg-green-500",
        primary:
          "bg-primary-700 text-white hover:!bg-primary-600 dark:bg-primary-700 dark:hover:!bg-primary-500",
        yellow:
          "bg-yellow-400 text-white hover:!bg-yellow-600 dark:bg-yellow-600 dark:hover:!bg-yellow-500",
        purple:
          "bg-purple-500 text-white hover:!bg-purple-600 dark:bg-purple-600 dark:hover:!bg-purple-500",
        outline:
          "bg-transparent border border-gray-300 hover:!bg-inherit text-gray-700 dark:bg-gray-600 dark:text-white dark:bg-gray-600 dark:hover:!bg-gray-600 hover:!bg-gray-100",
      },
    },
    defaultVariants: {
      variant: "default",
    },
  },
);

export type ButtonProps = Merge<
  VariantProps<typeof buttonVariants>,
  FlowbiteButtonProps & {
    isLoading?: boolean;
    disabledForInvalid?: boolean;
    enabledForDirty?: boolean;
  }
>;

export const Button = forwardRef<
  HTMLButtonElement,
  PropsWithChildren<ButtonProps>
>(
  (
    {
      className,
      variant,
      children,
      onClick,
      isLoading,
      disabled,
      disabledForInvalid = true,
      enabledForDirty,
      type,
      ...props
    }: PropsWithChildren<ButtonProps>,
    ref: ForwardedRef<HTMLButtonElement>,
  ) => {
    return (
      <FlowbiteButton
        ref={ref}
        type={type}
        className={cn(
          buttonVariants({ variant }),
          disabledForInvalid &&
            type === "submit" &&
            "group-[.is-invalid]:pointer-events-none group-[.is-invalid]:opacity-50",
          enabledForDirty &&
            type === "submit" &&
            "group-[.not-dirty]:pointer-events-none group-[.not-dirty]:opacity-50",
          isLoading && "cursor-progress",
          className,
        )}
        onClick={onClick}
        disabled={isLoading || disabled}
        {...props}
      >
        {isLoading && <Spinner size="sm" className="fill-primary-500" />}
        {children}
      </FlowbiteButton>
    );
  },
) as React.ForwardRefExoticComponent<
  PropsWithChildren<ButtonProps> & React.RefAttributes<HTMLButtonElement>
>;

Button.displayName = "Button";

type CloseButtonProps = ButtonProps & {
  onClose: () => void;
};

export const CloseButton = ({ onClose, ...props }: CloseButtonProps) => {
  return (
    <Button
      variant="outline"
      onClick={onClose}
      disabledForInvalid={false}
      {...props}
    >
      Cancel
    </Button>
  );
};
