"use client";

import { Card } from "flowbite-react";
import { useState } from "react";
import { FormProvider, useForm } from "react-hook-form";
import { HiPlus } from "react-icons/hi";
import { z } from "zod";

import { LayoutContent } from "@/components";
import { Button } from "@/components/ui/button";
import { Form } from "@/components/ui/form";

import { CategoriesTable } from "./categories-table";
import { CategoryModal } from "./category-modal";
import { CategoryVersionModal } from "./category-version-modal";
import { CategoryVersionSelect } from "./category-version-select";
import { ImportExportActions } from "./import-export-actions";

// Schema for the category version selection form
const categoryVersionSchema = z.object({
  selectedVersionId: z.string().optional(),
});

type CategoryVersionFormData = z.infer<typeof categoryVersionSchema>;

export const ArtifactCategoriesContent = () => {
  const [isVersionModalOpen, setIsVersionModalOpen] = useState(false);
  const [isCategoryModalOpen, setIsCategoryModalOpen] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState<any>(null);
  const [selectedVersionId, setSelectedVersionId] = useState<string>("");

  const handleAddCategory = () => {
    setSelectedCategory(null);
    setIsCategoryModalOpen(true);
  };

  const handleEditCategory = (category: any) => {
    setSelectedCategory(category);
    setIsCategoryModalOpen(true);
  };

  const handleCloseCategoryModal = () => {
    setIsCategoryModalOpen(false);
    setSelectedCategory(null);
  };

  return (
    <LayoutContent>
      <div className="p-6">
        <div className="mb-6">
          <h1 className="mb-2 text-3xl font-bold text-gray-900">
            Artifact Categories
          </h1>
          <p className="text-gray-500">
            Manage artifact categories and their versions for document
            classification.
          </p>
        </div>

        <FormProvider {...methods}>
          <Card
            className="mb-6"
            theme={{
              root: {
                children: "p-0",
              },
            }}
          >
            <div className="p-6">
              <div className="mb-2 flex items-center justify-between">
                <h2 className="text-xl font-semibold text-gray-900">
                  Category Version
                </h2>
                <Button
                  variant="primary"
                  onClick={() => setIsVersionModalOpen(true)}
                  className="flex items-center gap-2"
                >
                  <HiPlus className="h-4 w-4" />
                  Add New Version
                </Button>
              </div>
              <CategoryVersionSelect />
            </div>
          </Card>

          <Card
            theme={{
              root: {
                children: "p-0",
              },
            }}
          >
            <div className="">
              <div className="mb-1 flex items-center justify-between p-6">
                <h2 className="text-xl font-semibold text-gray-900">
                  Artifact Categories
                </h2>
                <div className="flex items-center gap-2">
                  <ImportExportActions selectedVersionId={selectedVersionId} />
                  <Button
                    variant="primary"
                    onClick={handleAddCategory}
                    disabled={!selectedVersionId}
                    className="flex items-center gap-2"
                  >
                    <HiPlus className="h-4 w-4" />
                    Add Category
                  </Button>
                </div>
              </div>

              {selectedVersionId ? (
                <CategoriesTable
                  versionId={selectedVersionId}
                  onEditCategory={handleEditCategory}
                />
              ) : (
                <div className="rounded-lg bg-gray-50 p-8 text-center">
                  <p className="text-gray-500">
                    Please select a category version to view and manage artifact
                    categories.
                  </p>
                </div>
              )}
            </div>
          </Card>
        </FormProvider>

        {/* Modals */}
        <CategoryVersionModal
          isOpen={isVersionModalOpen}
          onClose={() => setIsVersionModalOpen(false)}
        />

        <CategoryModal
          isOpen={isCategoryModalOpen}
          onClose={handleCloseCategoryModal}
          category={selectedCategory}
          versionId={selectedVersionId}
        />
      </div>
    </LayoutContent>
  );
};
